from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
from typing import Set


app = FastAPI(title="CodeMate WebSocket API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

# Store active connections
active_connections: Set[WebSocket] = set()


async def connect_websocket(websocket: WebSocket):
    """Handle new WebSocket connection"""
    await websocket.accept()
    active_connections.add(websocket)
    print(f"WebSocket connected. Total connections: {len(active_connections)}")


async def disconnect_websocket(websocket: WebSocket):
    """Handle WebSocket disconnection"""
    active_connections.discard(websocket)
    print(f"WebSocket disconnected. Total connections: {len(active_connections)}")


async def send_message(websocket: WebSocket, message: dict):
    """Send message to specific WebSocket connection"""
    try:
        await websocket.send_text(json.dumps(message))
    except Exception as e:
        print(f"Error sending message: {e}")
        await disconnect_websocket(websocket)


async def broadcast_message(message: dict):
    """Broadcast message to all active connections"""
    if not active_connections:
        return
    
    disconnected = set()
    for websocket in active_connections:
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            print(f"Error broadcasting to connection: {e}")
            disconnected.add(websocket)
    
    # Remove disconnected connections
    for websocket in disconnected:
        active_connections.discard(websocket)


async def handle_chat_stream(websocket: WebSocket, data: dict):
    """Handle streaming chat through WebSocket - Demo implementation"""
    llm_ = {
        "api_base": data.get("base_url", "https://llm.codemate.ai/v1"),
        "api_key": data.get("api_key", ""),
        "model": data.get("model", "bodh-x1"),
    }

    try:
        # Demo implementation - replace with actual chat_stream when logger is available
        messages = data.get("messages", [])

        # Send a demo response
        demo_response = f"Demo response for model {llm_['model']} with {len(messages)} messages"

        await send_message(websocket, {
            "type": "chat_stream",
            "data": {
                "index": 0,
                "type": "message",
                "message": demo_response
            }
        })

        # TODO: Replace with actual chat_stream integration:
        # from BASE.chat import chat_stream
        # async for chunk in chat_stream(llm_=llm_, messages=data["messages"], call_for="chat"):
        #     # Parse the chunk and send it through WebSocket
        #     if chunk.strip():
        #         # Remove the delimiter if present
        #         clean_chunk = chunk.replace("<__!!__END__!!__>", "")
        #         if clean_chunk.strip():
        #             try:
        #                 chunk_data = json.loads(clean_chunk)
        #                 await send_message(websocket, {
        #                     "type": "chat_stream",
        #                     "data": chunk_data
        #                 })
        #             except json.JSONDecodeError:
        #                 # If it's not JSON, send as plain text
        #                 await send_message(websocket, {
        #                     "type": "chat_stream",
        #                     "data": {"message": clean_chunk}
        #                 })

    except Exception as e:
        await send_message(websocket, {
            "type": "error",
            "message": f"Chat stream error: {str(e)}"
        })


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Main WebSocket endpoint"""
    await connect_websocket(websocket)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type", "")
                
                if message_type == "chat":
                    # Handle chat streaming
                    await handle_chat_stream(websocket, message.get("data", {}))
                
                elif message_type == "ping":
                    # Handle ping/pong for connection health
                    await send_message(websocket, {"type": "pong"})
                
                elif message_type == "broadcast":
                    # Handle broadcast to all connections
                    await broadcast_message({
                        "type": "broadcast",
                        "data": message.get("data", {})
                    })
                
                else:
                    # Echo unknown message types
                    await send_message(websocket, {
                        "type": "echo",
                        "data": message
                    })
                    
            except json.JSONDecodeError:
                await send_message(websocket, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
                
    except WebSocketDisconnect:
        await disconnect_websocket(websocket)
    except Exception as e:
        print(f"WebSocket error: {e}")
        await disconnect_websocket(websocket)


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=45214)
