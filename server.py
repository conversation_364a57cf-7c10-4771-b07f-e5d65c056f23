from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uvicorn
from BASE.chat import chat_stream


app = FastAPI(title="CodeMate HTTP API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.post("/chat/stream")
async def stream(request: Request):
    data = await request.json()
    llm_ = {
        "api_base": data.get("base_url", "https://llm.codemate.ai/v1"),
        "api_key": data.get("api_key", ""),
        "model": data.get("model", "bodh-x1"),
    }
    return StreamingResponse(chat_stream(llm_=llm_, messages=data["messages"], call_for="chat"), media_type="text/event-stream")


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=45213)