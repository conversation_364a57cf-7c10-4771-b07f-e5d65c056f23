import json
from qdrant_client.models import <PERSON><PERSON><PERSON><PERSON>, Filter, MatchText
import os
from qdrant_client import AsyncQdrantClient



async def search(qdrant_instance: AsyncQdrantClient, query: str, index_name: str, folder_path: str = None, limit: int = 30, is_local: bool = False): 
    """
    Perform search within a specific knowledge-base and even in a folder of a knowledge-base. 
    """

    