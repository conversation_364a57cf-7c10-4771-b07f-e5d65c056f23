from ipc import IPC
from BASE.utils.platform_detector import PlatformDetector

"""
We create an IPC instance for shared memory communication.
Rather than using events (for now), we use a shared pool to manage states.
"""
ipc_ = IPC.connect()
ipc_.set("platform", PlatformDetector.get_os_name())
ipc_.set("ollama_running", False)
ipc_.set("internet_connected", False)
ipc_.set("internet_available", False)